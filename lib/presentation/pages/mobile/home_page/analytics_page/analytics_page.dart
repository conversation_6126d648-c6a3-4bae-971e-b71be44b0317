import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:langda/backend/service/analytics_service.dart';
import 'package:langda/common/LDState.dart';
import 'package:langda/common/custom_app_bar.dart';
import 'package:langda/presentation/pages/mobile/constants.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class AnalyticsPage extends StatefulWidget {
  const AnalyticsPage({super.key});

  @override
  State<AnalyticsPage> createState() => _AnalyticsPageState();
}

class _AnalyticsPageState extends LDState<AnalyticsPage> {
  Map<String, dynamic>? statsData;
  List<FlSpot>? diaryStreakData;
  List<BarChartGroupData>? weeklyDiaryData;
  List<PieChartSectionData>? cardPerformanceData;
  List<FlSpot>? practiceAccuracyData;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAnalyticsData();
  }

  Future<void> _loadAnalyticsData() async {
    try {
      final results = await Future.wait([
        AnalyticsService.getStatsSummary(),
        AnalyticsService.getDiaryStreakData(),
        AnalyticsService.getWeeklyDiaryData(),
        AnalyticsService.getCardPerformanceData(),
        AnalyticsService.getPracticeAccuracyData(),
      ]);

      setState(() {
        statsData = results[0] as Map<String, dynamic>;
        diaryStreakData = results[1] as List<FlSpot>;
        weeklyDiaryData = results[2] as List<BarChartGroupData>;
        cardPerformanceData = results[3] as List<PieChartSectionData>;
        practiceAccuracyData = results[4] as List<FlSpot>;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget buildContent(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: customAppBar(
        title: 'Analytics',
        leadingWidget: Image.asset(
          'assets/images/icons/chevron-left_1.5.png',
          width: 48,
          fit: BoxFit.scaleDown,
          scale: 1.3,
        ),
        leadingLabel: 'Back',
        leadingOnTap: () {
          Navigator.of(context).pop();
        },
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildStatsOverview(),
                  SizedBox(height: 24.sp),
                  _buildDiaryStreakChart(),
                  SizedBox(height: 24.sp),
                  _buildWeeklyDiaryChart(),
                  SizedBox(height: 24.sp),
                  _buildCardPerformanceChart(),
                  SizedBox(height: 24.sp),
                  _buildPracticeAccuracyChart(),
                  SizedBox(height: 24.sp),
                ],
              ),
            ),
    );
  }

  Widget _buildStatsOverview() {
    if (statsData == null) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.all(16.sp),
      decoration: BoxDecoration(
        color: LDColors.foundationLime.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Overview',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 12.sp),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Diaries',
                  '${statsData!['totalDiaries']}',
                  Icons.book,
                ),
              ),
              SizedBox(width: 12.sp),
              Expanded(
                child: _buildStatCard(
                  'Current Streak',
                  '${statsData!['currentStreak']} days',
                  Icons.local_fire_department,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.sp),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Cards',
                  '${statsData!['totalCards']}',
                  Icons.credit_card,
                ),
              ),
              SizedBox(width: 12.sp),
              Expanded(
                child: _buildStatCard(
                  'Mastered',
                  '${statsData!['masteredCards']}',
                  Icons.star,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon) {
    return Container(
      padding: EdgeInsets.all(12.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        children: [
          Icon(icon, color: LDColors.foundationLime, size: 24.sp),
          SizedBox(height: 8.sp),
          Text(
            value,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDiaryStreakChart() {
    if (diaryStreakData == null) return const SizedBox.shrink();

    return _buildChartContainer(
      title: 'Diary Writing Streak (Last 30 Days)',
      child: SizedBox(
        height: 200,
        child: LineChart(
          LineChartData(
            gridData: const FlGridData(show: false),
            titlesData: const FlTitlesData(show: false),
            borderData: FlBorderData(show: false),
            lineBarsData: [
              LineChartBarData(
                spots: diaryStreakData!,
                isCurved: true,
                color: LDColors.foundationLime,
                barWidth: 3,
                dotData: const FlDotData(show: false),
                belowBarData: BarAreaData(
                  show: true,
                  color: LDColors.foundationLime.withValues(alpha: 0.3),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWeeklyDiaryChart() {
    if (weeklyDiaryData == null) return const SizedBox.shrink();

    return _buildChartContainer(
      title: 'Weekly Diary Count (Last 8 Weeks)',
      child: SizedBox(
        height: 200,
        child: BarChart(
          BarChartData(
            alignment: BarChartAlignment.spaceAround,
            maxY: weeklyDiaryData!
                    .map((e) => e.barRods.first.toY)
                    .reduce((a, b) => a > b ? a : b) +
                2,
            barTouchData: BarTouchData(enabled: false),
            titlesData: const FlTitlesData(show: false),
            borderData: FlBorderData(show: false),
            barGroups: weeklyDiaryData!,
            gridData: const FlGridData(show: false),
          ),
        ),
      ),
    );
  }

  Widget _buildCardPerformanceChart() {
    if (cardPerformanceData == null) return const SizedBox.shrink();

    return _buildChartContainer(
      title: 'Card Performance by Category',
      child: SizedBox(
        height: 200,
        child: PieChart(
          PieChartData(
            sections: cardPerformanceData!,
            centerSpaceRadius: 40,
            sectionsSpace: 2,
          ),
        ),
      ),
    );
  }

  Widget _buildPracticeAccuracyChart() {
    if (practiceAccuracyData == null) return const SizedBox.shrink();

    return _buildChartContainer(
      title: 'Practice Accuracy Trend',
      child: SizedBox(
        height: 200,
        child: LineChart(
          LineChartData(
            gridData: const FlGridData(show: true),
            titlesData: FlTitlesData(
              leftTitles: AxisTitles(
                sideTitles: SideTitles(
                  showTitles: true,
                  reservedSize: 40,
                  getTitlesWidget: (value, meta) {
                    return Text('${value.toInt()}%',
                        style: TextStyle(fontSize: 10.sp));
                  },
                ),
              ),
              bottomTitles:
                  const AxisTitles(sideTitles: SideTitles(showTitles: false)),
              topTitles:
                  const AxisTitles(sideTitles: SideTitles(showTitles: false)),
              rightTitles:
                  const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            ),
            borderData: FlBorderData(show: true),
            minY: 0,
            maxY: 100,
            lineBarsData: [
              LineChartBarData(
                spots: practiceAccuracyData!,
                isCurved: true,
                color: Colors.blue,
                barWidth: 3,
                dotData: FlDotData(
                  show: true,
                  getDotPainter: (spot, percent, barData, index) {
                    return FlDotCirclePainter(
                      radius: 4,
                      color: Colors.blue,
                      strokeWidth: 2,
                      strokeColor: Colors.white,
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChartContainer({required String title, required Widget child}) {
    return Container(
      padding: EdgeInsets.all(16.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade100,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.sp),
          child,
        ],
      ),
    );
  }
}
