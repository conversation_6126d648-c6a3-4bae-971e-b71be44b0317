import 'package:flutter/material.dart';
import 'package:langda/backend/model/practice_problem_model.dart';
import 'package:langda/presentation/pages/mobile/constants.dart';

enum AnswerState {
  unanswered,
  correct,
  incorrect,
}

class PracticeProblemCard extends StatefulWidget {
  final PracticeProblem problem;
  final Function(String answer, bool isCorrect) onAnswerSelected;
  final bool isAnswered;
  final String? selectedAnswer;

  const PracticeProblemCard({
    super.key,
    required this.problem,
    required this.onAnswerSelected,
    this.isAnswered = false,
    this.selectedAnswer,
  });

  @override
  State<PracticeProblemCard> createState() => _PracticeProblemCardState();
}

class _PracticeProblemCardState extends State<PracticeProblemCard>
    with SingleTickerProviderStateMixin {
  String? _selectedAnswer;
  AnswerState _answerState = AnswerState.unanswered;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late TextEditingController _textController;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _selectedAnswer = widget.selectedAnswer;
    if (widget.isAnswered && _selectedAnswer != null) {
      _answerState = widget.problem.isCorrectAnswer(_selectedAnswer!)
          ? AnswerState.correct
          : AnswerState.incorrect;
    }

    _textController = TextEditingController();
    _focusNode = FocusNode();

    // If there's a selected answer, populate the text field
    if (_selectedAnswer != null) {
      _textController.text = _selectedAnswer!;
    }

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _handleTextInput(String input) {
    if (widget.isAnswered) return;

    final trimmedInput = input.trim();
    if (trimmedInput.isEmpty) return;

    setState(() {
      _selectedAnswer = trimmedInput;
      _answerState = widget.problem.isCorrectAnswer(trimmedInput)
          ? AnswerState.correct
          : AnswerState.incorrect;
    });

    // Unfocus the text field
    _focusNode.unfocus();

    // Trigger animation
    _animationController.forward().then((_) {
      _animationController.reverse();
    });

    // Call the callback
    widget.onAnswerSelected(
        trimmedInput, widget.problem.isCorrectAnswer(trimmedInput));
  }

  void _handleRealTimeInput(String input) {
    if (widget.isAnswered) return;

    final trimmedInput = input.trim();

    // Only start checking after 2 or more characters
    if (trimmedInput.length < 2) {
      setState(() {
        _answerState = AnswerState.unanswered;
      });
      return;
    }

    setState(() {
      _answerState = widget.problem.isCorrectAnswer(trimmedInput)
          ? AnswerState.correct
          : AnswerState.incorrect;
    });

    // If the answer is correct, automatically submit it
    if (_answerState == AnswerState.correct) {
      _selectedAnswer = trimmedInput;

      // Trigger animation
      _animationController.forward().then((_) {
        _animationController.reverse();
      });

      // Call the callback
      widget.onAnswerSelected(
          trimmedInput, widget.problem.isCorrectAnswer(trimmedInput));
    }
  }

  Widget _buildSentenceWithTextField() {
    final words = widget.problem.sentence.split(' ');
    final blankPosition = widget.problem.blankPosition;

    if (blankPosition < 0 || blankPosition >= words.length) {
      // Fallback to original sentence if blank position is invalid
      return Text(
        widget.problem.sentence,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
          height: 1.4,
        ),
        textAlign: TextAlign.center,
      );
    }

    List<Widget> sentenceWidgets = [];

    for (int i = 0; i < words.length; i++) {
      if (i == blankPosition) {
        // Add the text field for the blank
        sentenceWidgets.add(
          Container(
            width: 100,
            height: 35,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: _getTextFieldBorderColor(),
                  width: 2,
                ),
              ),
            ),
            child: TextField(
              controller: _textController,
              focusNode: _focusNode,
              enabled: !widget.isAnswered,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: _getTextFieldTextColor(),
              ),
              decoration: const InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.only(bottom: 8),
              ),
              onChanged: _handleRealTimeInput,
              onSubmitted: _handleTextInput,
              onTapOutside: (event) {
                if (_textController.text.trim().isNotEmpty) {
                  _handleTextInput(_textController.text);
                }
              },
            ),
          ),
        );
      } else {
        // Add regular text
        sentenceWidgets.add(
          Text(
            words[i],
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
              height: 1.4,
            ),
          ),
        );
      }

      // Add space between words (except for the last word)
      if (i < words.length - 1) {
        sentenceWidgets.add(
          const Text(
            ' ',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
              height: 1.4,
            ),
          ),
        );
      }
    }

    return Wrap(
      alignment: WrapAlignment.center,
      crossAxisAlignment: WrapCrossAlignment.center,
      children: sentenceWidgets,
    );
  }

  Color _getTextFieldBorderColor() {
    if (!widget.isAnswered) {
      return LDColors.mainLime;
    }

    switch (_answerState) {
      case AnswerState.correct:
        return Colors.green;
      case AnswerState.incorrect:
        return Colors.red;
      case AnswerState.unanswered:
        return LDColors.mainLime;
    }
  }

  Color _getTextFieldTextColor() {
    if (!widget.isAnswered) {
      return Colors.black87;
    }

    switch (_answerState) {
      case AnswerState.correct:
        return Colors.green.shade700;
      case AnswerState.incorrect:
        return Colors.red.shade700;
      case AnswerState.unanswered:
        return Colors.black87;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
              border: Border.all(
                color: LDColors.lightGrey,
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Category badge (if available)
                if (widget.problem.category != null) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: LDColors.foundationLimeLight,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      widget.problem.category!,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: LDColors.foundationLimeDark,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // Sentence with blank
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: LDColors.lightGrey,
                      width: 1,
                    ),
                  ),
                  child: _buildSentenceWithTextField(),
                ),

                const SizedBox(height: 10),

                // Explanation (shown after answering)
                if (widget.isAnswered &&
                    widget.problem.explanation != null) ...[
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.blue.shade200,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.lightbulb_outline,
                              color: Colors.blue.shade700,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Explanation',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Colors.blue.shade700,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          widget.problem.explanation!,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.blue.shade800,
                            height: 1.4,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}
